<template>
  <div class="pdf-translate-container">
    <div class="page-header">
      <page-header :title="$t('PDF翻译')">
        <template #titleWarning>{{ $t("上传PDF文件进行智能翻译") }}</template>
      </page-header>
    </div>

    <div class="translate-content">
      <el-row :gutter="20">
        <!-- 左侧上传区域 -->
        <el-col :span="12">
          <el-card shadow="hover" class="upload-card">
            <div slot="header" class="card-header">
              <span>文件上传</span>
            </div>

            <div class="upload-area">
              <el-upload
                class="upload-dragger"
                drag
                :action="uploadUrl"
                :before-upload="beforeUpload"
                :on-success="handleUploadSuccess"
                :on-error="handleUploadError"
                :file-list="fileList"
                accept=".pdf"
              >
                <i class="el-icon-upload"></i>
                <div class="el-upload__text">将PDF文件拖到此处，或<em>点击上传</em></div>
                <div class="el-upload__tip" slot="tip">只能上传PDF文件，且不超过10MB</div>
              </el-upload>
            </div>

            <!-- 翻译设置 -->
            <div class="translate-settings" v-if="fileList.length > 0">
              <el-divider content-position="left">翻译设置</el-divider>

              <el-form :model="translateForm" label-width="100px">
                <el-form-item label="源语言">
                  <el-select v-model="translateForm.sourceLang" placeholder="请选择源语言">
                    <el-option label="自动检测" value="auto"></el-option>
                    <el-option label="中文" value="zh"></el-option>
                    <el-option label="英文" value="en"></el-option>
                    <el-option label="日文" value="ja"></el-option>
                    <el-option label="韩文" value="ko"></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="目标语言">
                  <el-select v-model="translateForm.targetLang" placeholder="请选择目标语言">
                    <el-option label="中文" value="zh"></el-option>
                    <el-option label="英文" value="en"></el-option>
                    <el-option label="日文" value="ja"></el-option>
                    <el-option label="韩文" value="ko"></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="翻译模式">
                  <el-radio-group v-model="translateForm.mode">
                    <el-radio label="fast">快速翻译</el-radio>
                    <el-radio label="accurate">精准翻译</el-radio>
                  </el-radio-group>
                </el-form-item>

                <el-form-item>
                  <el-button
                    type="primary"
                    @click="startTranslate"
                    :loading="translating"
                    :disabled="!canTranslate"
                  >
                    {{ translating ? '翻译中...' : '开始翻译' }}
                  </el-button>
                </el-form-item>
              </el-form>
            </div>
          </el-card>
        </el-col>

        <!-- 右侧预览区域 -->
        <el-col :span="12">
          <el-card shadow="hover" class="preview-card">
            <div slot="header" class="card-header">
              <span>翻译预览</span>
              <div class="header-actions" v-if="translateResult">
                <el-button size="small" @click="downloadTranslated">下载译文</el-button>
              </div>
            </div>

            <div class="preview-content">
              <div v-if="!translateResult" class="empty-preview">
                <i class="el-icon-document"></i>
                <p>上传PDF文件后，翻译结果将在此处显示</p>
              </div>

              <div v-else class="translate-result">
                <div class="progress-info" v-if="translating">
                  <el-progress :percentage="translateProgress"></el-progress>
                  <p class="progress-text">正在翻译第 {{ currentPage }} / {{ totalPages }} 页</p>
                </div>

                <div class="result-content" v-else>
                  <div class="page-navigation">
                    <el-pagination
                      @current-change="handlePageChange"
                      :current-page="currentPreviewPage"
                      :page-size="1"
                      layout="prev, pager, next"
                      :total="translateResult.pages.length"
                      small
                    >
                    </el-pagination>
                  </div>

                  <div class="page-content">
                    <div class="original-text">
                      <h4>原文</h4>
                      <div class="text-content">
                        {{ translateResult.pages[currentPreviewPage - 1]?.original || '' }}
                      </div>
                    </div>

                    <el-divider></el-divider>

                    <div class="translated-text">
                      <h4>译文</h4>
                      <div class="text-content">
                        {{ translateResult.pages[currentPreviewPage - 1]?.translated || '' }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
export default {
  name: "PdfTranslate",
  data() {
    return {
      fileList: [],
      uploadUrl: process.env.VUE_APP_BASE_API + "/common/upload",
      translateForm: {
        sourceLang: 'auto',
        targetLang: 'zh',
        mode: 'fast'
      },
      translating: false,
      translateProgress: 0,
      currentPage: 0,
      totalPages: 0,
      translateResult: null,
      currentPreviewPage: 1
    };
  },
  computed: {
    canTranslate() {
      return this.fileList.length > 0 && this.translateForm.targetLang && !this.translating;
    }
  },
  methods: {
    beforeUpload(file) {
      const isPDF = file.type === 'application/pdf';
      const isLt10M = file.size / 1024 / 1024 < 10;

      if (!isPDF) {
        this.$message.error('只能上传PDF格式的文件!');
        return false;
      }
      if (!isLt10M) {
        this.$message.error('上传文件大小不能超过 10MB!');
        return false;
      }
      return true;
    },

    handleUploadSuccess(response, file) {
      this.$message.success('文件上传成功');
      // 处理上传成功后的逻辑
    },

    handleUploadError(error) {
      this.$message.error('文件上传失败');
      console.error('Upload error:', error);
    },

    startTranslate() {
      if (!this.canTranslate) return;

      this.translating = true;
      this.translateProgress = 0;
      this.currentPage = 0;
      this.totalPages = 10; // 示例数据，实际应从API获取

      // 模拟翻译进度
      this.simulateTranslation();
    },

    simulateTranslation() {
      const interval = setInterval(() => {
        this.translateProgress += 10;
        this.currentPage = Math.floor(this.translateProgress / 10);

        if (this.translateProgress >= 100) {
          clearInterval(interval);
          this.translating = false;
          this.translateResult = {
            pages: [
              {
                original: "This is a sample PDF content in English that needs to be translated.",
                translated: "这是一个需要翻译的英文PDF内容示例。"
              },
              {
                original: "Another page with different content for translation.",
                translated: "另一页包含不同翻译内容的页面。"
              }
            ]
          };
          this.$message.success('翻译完成！');
        }
      }, 500);
    },

    handlePageChange(page) {
      this.currentPreviewPage = page;
    },

    downloadTranslated() {
      // 实现下载翻译后的文件
      this.$message.info('下载功能开发中...');
    }
  }
};
</script>

<style lang="scss" scoped>
.pdf-translate-container {
  padding: 20px;

  .page-header {
    margin-bottom: 20px;
  }

  .translate-content {
    .upload-card, .preview-card {
      height: 600px;

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: bold;
      }
    }

    .upload-area {
      margin-bottom: 20px;

      .upload-dragger {
        width: 100%;
      }
    }

    .translate-settings {
      margin-top: 20px;
    }

    .preview-content {
      height: 500px;

      .empty-preview {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: #999;

        i {
          font-size: 48px;
          margin-bottom: 16px;
        }
      }

      .translate-result {
        height: 100%;
        display: flex;
        flex-direction: column;

        .progress-info {
          text-align: center;
          padding: 20px;

          .progress-text {
            margin-top: 10px;
            color: #666;
          }
        }

        .result-content {
          flex: 1;
          display: flex;
          flex-direction: column;

          .page-navigation {
            text-align: center;
            margin-bottom: 20px;
          }

          .page-content {
            flex: 1;
            overflow-y: auto;

            .original-text, .translated-text {
              h4 {
                margin: 0 0 10px 0;
                color: #333;
              }

              .text-content {
                padding: 15px;
                background: #f5f5f5;
                border-radius: 4px;
                line-height: 1.6;
                min-height: 100px;
                white-space: pre-wrap;
              }
            }
          }
        }
      }
    }
  }
}
</style>
