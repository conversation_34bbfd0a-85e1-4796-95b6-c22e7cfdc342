<template>
  <div class="pdf-translate-container">
    <div class="translate-content">
      <i-frame src="/ai/pdftrans" style="flex: 1" id="bdIframe" />
    </div>
  </div>
</template>

<script>
import iFrame from "@/components/iFrame/index.vue";

export default {
  name: "PdfTranslate",
  components: { iFrame },
  data() {
    return {
      fileList: [],
      uploadUrl: '/api/upload/pdf', // 需要根据实际API调整
      translateForm: {
        sourceLang: 'auto',
        targetLang: 'zh',
        mode: 'fast'
      },
      translating: false,
      translateProgress: 0,
      currentPage: 0,
      totalPages: 0,
      translateResult: null,
      currentPreviewPage: 1
    };
  },
  computed: {
    canTranslate() {
      return this.fileList.length > 0 && this.translateForm.targetLang && !this.translating;
    }
  },
  methods: {
    beforeUpload(file) {
      const isPDF = file.type === 'application/pdf';
      const isLt10M = file.size / 1024 / 1024 < 10;

      if (!isPDF) {
        this.$message.error('只能上传PDF格式的文件!');
        return false;
      }
      if (!isLt10M) {
        this.$message.error('上传文件大小不能超过 10MB!');
        return false;
      }
      return true;
    },

    handleUploadSuccess(response, file) {
      this.$message.success('文件上传成功');
      // 处理上传成功后的逻辑
    },

    handleUploadError(error) {
      this.$message.error('文件上传失败');
      console.error('Upload error:', error);
    },

    startTranslate() {
      if (!this.canTranslate) return;

      this.translating = true;
      this.translateProgress = 0;
      this.currentPage = 0;
      this.totalPages = 10; // 示例数据，实际应从API获取

      // 模拟翻译进度
      this.simulateTranslation();
    },

    simulateTranslation() {
      const interval = setInterval(() => {
        this.translateProgress += 10;
        this.currentPage = Math.floor(this.translateProgress / 10);

        if (this.translateProgress >= 100) {
          clearInterval(interval);
          this.translating = false;
          this.translateResult = {
            pages: [
              {
                original: "This is a sample PDF content in English that needs to be translated.",
                translated: "这是一个需要翻译的英文PDF内容示例。"
              },
              {
                original: "Another page with different content for translation.",
                translated: "另一页包含不同翻译内容的页面。"
              }
            ]
          };
          this.$message.success('翻译完成！');
        }
      }, 500);
    },

    handlePageChange(page) {
      this.currentPreviewPage = page;
    },

    downloadTranslated() {
      // 实现下载翻译后的文件
      this.$message.info('下载功能开发中...');
    }
  }
};
</script>

<style lang="scss" scoped>
.pdf-translate-container {
  padding: 20px;

  .page-header {
    margin-bottom: 20px;
  }

  .translate-content {
    .upload-card, .preview-card {
      height: 600px;

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: bold;
      }
    }

    .upload-area {
      margin-bottom: 20px;

      .upload-dragger {
        width: 100%;
      }
    }

    .translate-settings {
      margin-top: 20px;
    }

    .preview-content {
      height: 500px;

      .empty-preview {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: #999;

        i {
          font-size: 48px;
          margin-bottom: 16px;
        }
      }

      .translate-result {
        height: 100%;
        display: flex;
        flex-direction: column;

        .progress-info {
          text-align: center;
          padding: 20px;

          .progress-text {
            margin-top: 10px;
            color: #666;
          }
        }

        .result-content {
          flex: 1;
          display: flex;
          flex-direction: column;

          .page-navigation {
            text-align: center;
            margin-bottom: 20px;
          }

          .page-content {
            flex: 1;
            overflow-y: auto;

            .original-text, .translated-text {
              h4 {
                margin: 0 0 10px 0;
                color: #333;
              }

              .text-content {
                padding: 15px;
                background: #f5f5f5;
                border-radius: 4px;
                line-height: 1.6;
                min-height: 100px;
                white-space: pre-wrap;
              }
            }
          }
        }
      }
    }
  }
}
</style>
