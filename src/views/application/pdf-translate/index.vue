<template>
  <div class="pdf-translate-container">
    <div class="page-header">
      <page-header :title="$t('PDF翻译')">
        <template #titleWarning>{{ $t("上传PDF文件进行智能翻译") }}</template>
      </page-header>
    </div>

    <div class="translate-content">
      <el-row :gutter="20">
        <!-- 左侧上传区域 -->
        <el-col :span="12">
          <el-card shadow="hover" class="upload-card">
            <div slot="header" class="card-header">
              <span>文件上传</span>
            </div>

            <div class="upload-area">
              <el-upload
                ref="upload"
                class="upload-dragger"
                drag
                :auto-upload="false"
                :limit="1"
                :on-exceed="handleExceed"
                :before-upload="beforeUpload"
                :on-change="handleFileChange"
                :file-list="fileList"
                accept=".pdf"
              >
                <i class="el-icon-upload"></i>
                <div class="el-upload__text">将PDF文件拖到此处，或<em>点击上传</em></div>
                <div class="el-upload__tip" slot="tip">只能上传一个PDF文件，且不超过20MB</div>
              </el-upload>
            </div>

            <!-- 文件信息 -->
            <div class="file-info" v-if="currentFile">
              <el-divider content-position="left">文件信息</el-divider>
              <div class="file-details">
                <p><strong>文件名：</strong>{{ currentFile.name }}</p>
                <p><strong>文件大小：</strong>{{ formatFileSize(currentFile.size) }}</p>
                <p><strong>上传时间：</strong>{{ new Date().toLocaleString() }}</p>
              </div>

              <div class="translate-actions">
                <el-button
                  type="primary"
                  @click="startTranslate"
                  :loading="translating"
                  :disabled="!currentFile || translating"
                  size="medium"
                >
                  {{ translating ? '翻译中...' : '开始翻译' }}
                </el-button>
                <el-button
                  @click="clearFile"
                  :disabled="translating"
                  size="medium"
                >
                  重新选择
                </el-button>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 右侧预览区域 -->
        <el-col :span="12">
          <el-card shadow="hover" class="preview-card">
            <div slot="header" class="card-header">
              <span>翻译结果</span>
              <div class="header-actions" v-if="translatedPdfUrl">
                <el-button size="small" @click="downloadTranslated">下载译文</el-button>
              </div>
            </div>

            <div class="preview-content">
              <div v-if="translating" class="translating-info">
                <div class="loading-animation">
                  <i class="el-icon-loading"></i>
                </div>
                <p class="loading-text">正在翻译PDF文件，请稍候...</p>
                <el-progress :percentage="translateProgress" :show-text="false"></el-progress>
              </div>

              <div v-else-if="!translatedPdfUrl" class="empty-preview">
                <i class="el-icon-document"></i>
                <p>上传PDF文件并点击"开始翻译"后，翻译结果将在此处显示</p>
              </div>

              <div v-else class="pdf-preview">
                <iframe
                  :src="translatedPdfUrl"
                  class="pdf-iframe"
                  frameborder="0"
                >
                  您的浏览器不支持PDF预览，请<a :href="translatedPdfUrl" target="_blank">点击此处下载</a>查看翻译结果。
                </iframe>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import axios from 'axios';
import JSZip from 'jszip';

export default {
  name: "PdfTranslate",
  data() {
    return {
      fileList: [],
      currentFile: null,
      translating: false,
      translateProgress: 0,
      translatedPdfUrl: null,
      translatedFileName: null
    };
  },
  methods: {
    beforeUpload(file) {
      const isPDF = file.type === 'application/pdf';
      const isLt20M = file.size / 1024 / 1024 < 20;

      if (!isPDF) {
        this.$message.error('只能上传PDF格式的文件!');
        return false;
      }
      if (!isLt20M) {
        this.$message.error('上传文件大小不能超过 20MB!');
        return false;
      }
      return false; // 阻止自动上传
    },

    handleFileChange(file, fileList) {
      if (fileList.length > 0) {
        this.currentFile = file.raw;
        this.fileList = [file];
        // 清除之前的翻译结果
        this.translatedPdfUrl = null;
        this.translatedFileName = null;
      }
    },

    handleExceed(files, fileList) {
      this.$message.warning('只能上传一个PDF文件，请先删除当前文件再上传新文件');
    },

    clearFile() {
      this.currentFile = null;
      this.fileList = [];
      this.translatedPdfUrl = null;
      this.translatedFileName = null;
      this.$refs.upload && this.$refs.upload.clearFiles();
    },

    formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes';
      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

<style lang="scss" scoped>
.pdf-translate-container {
  padding: 20px;

  .page-header {
    margin-bottom: 20px;
  }

  .translate-content {
    .upload-card, .preview-card {
      height: 600px;

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: bold;
      }
    }

    .upload-area {
      margin-bottom: 20px;

      .upload-dragger {
        width: 100%;
      }
    }

    .translate-settings {
      margin-top: 20px;
    }

    .preview-content {
      height: 500px;

      .empty-preview {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: #999;

        i {
          font-size: 48px;
          margin-bottom: 16px;
        }
      }

      .translate-result {
        height: 100%;
        display: flex;
        flex-direction: column;

        .progress-info {
          text-align: center;
          padding: 20px;

          .progress-text {
            margin-top: 10px;
            color: #666;
          }
        }

        .result-content {
          flex: 1;
          display: flex;
          flex-direction: column;

          .page-navigation {
            text-align: center;
            margin-bottom: 20px;
          }

          .page-content {
            flex: 1;
            overflow-y: auto;

            .original-text, .translated-text {
              h4 {
                margin: 0 0 10px 0;
                color: #333;
              }

              .text-content {
                padding: 15px;
                background: #f5f5f5;
                border-radius: 4px;
                line-height: 1.6;
                min-height: 100px;
                white-space: pre-wrap;
              }
            }
          }
        }
      }
    }
  }
}
</style>
