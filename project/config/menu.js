export const menu = [
  {
    alwaysShow: false,
    component: "Layout",
    hidden: false,
    meta: {
      title: "Dify",
      titleEn: "component Demo",
      icon: "icon-xinhuihua",
      noCache: true,
      link: "",
      activeMenu: "",
    },
    name: "difyIframe",
    path: "/app3",
    children: [
      {
        component: "difyIframe/new",
        hidden: false,
        meta: {
          title: "新会话",
          titleEn: "New Session",
          icon: "",
          noCache: true,
          link: "",
          activeMenu: "",
        },
        name: "newDifyPage",
        path: "/dify/new",
        query: "",
        pid: "1698621081462707497",
        id: "1698621081462706324",
      },
    ],
    query: "",
    pid: "",
  },
  {
    alwaysShow: false,
    component: "Layout",
    hidden: false,
    meta: {
      title: "应用",
      titleEn: "component Demo",
      icon: "app",
      noCache: true,
      link: "",
      activeMenu: "",
    },
    name: "component",
    path: "/app",
    children: [
      {
        component: "application/app/index",
        hidden: false,
        meta: {
          title: "应用探索",
          titleEn: "Application Discovery",
          icon: "",
          noCache: true,
          link: "",
          activeMenu: "",
        },
        name: "demo",
        path: "/app",
        query: "",
        pid: "1698621081462707497",
        id: "1698621081462706324",
      },
      {
        component: "application/pdf-translate/index",
        hidden: false,
        meta: {
          title: "PDF翻译",
          titleEn: "PDF Translation",
          icon: "",
          noCache: true,
          link: "",
          activeMenu: "",
        },
        name: "pdfTranslate",
        path: "/app/pdf-translate",
        query: "",
        pid: "1698621081462707497",
        id: "1698621081462706325",
      },
    ],
    query: "",
    pid: "",
  },
  {
    alwaysShow: false,
    component: "Layout",
    hidden: true,
    meta: {
      title: "Dify",
      titleEn: "component Demo",
      icon: "ca",
      noCache: true,
      link: "",
      activeMenu: "",
    },
    name: "difyIframe",
    path: "/app2",
    children: [
      {
        component: "difyIframe/index",
        hidden: false,
        meta: {
          title: "Dify",
          titleEn: "demo",
          icon: "",
          noCache: true,
          link: "",
          activeMenu: "",
        },
        name: "difyPage",
        path: "/dify/index",
        query: "",
        pid: "1698621081462707497",
        id: "1698621081462706324",
      },
    ],
    query: "",
    pid: "",
  },
  // {
  //   alwaysShow: true,
  //   component: "Layout",
  //   hidden: false,
  //   meta: {
  //     title: "组件示例",
  //     titleEn: "component Demo",
  //     icon: "ca",
  //     noCache: true,
  //     link: "",
  //     activeMenu: "",
  //   },
  //   name: "component",
  //   path: "/component",
  //   children: [
  //     // {
  //     //   alwaysShow: true,
  //     //   component: "ParentView",
  //     //   hidden: false,
  //     //   meta: {
  //     //     title: "字典示例",
  //     //     titleEn: "Dict",
  //     //     icon: "",
  //     //     noCache: true,
  //     //     link: "",
  //     //     activeMenu: "",
  //     //   },
  //     //   name: "demo",
  //     //   path: "/dict/demo",
  //     //   children: [
  //     {
  //       alwaysShow: false,
  //       component: "demo/dict/index",
  //       hidden: false,
  //       meta: {
  //         title: "字典Tag组件示例",
  //         titleEn: "Dict Style",
  //         icon: "",
  //         noCache: true,
  //         link: "",
  //         activeMenu: "",
  //       },
  //       name: "dictDemo1",
  //       path: "/dict/demo/style1",
  //       children: [],
  //       query: "",
  //       pid: "1698621081462706326",
  //       id: "1698621081462706324",
  //     },
  //     //   ],
  //     //   query: "",
  //     //   pid: "1698621081462707497",
  //     //   id: "1698621081462706326",
  //     // },
  //     // {
  //     //   alwaysShow: true,
  //     //   component: "ParentView",
  //     //   hidden: false,
  //     //   meta: {
  //     //     title: "按钮示例",
  //     //     titleEn: "Btn",
  //     //     icon: "",
  //     //     noCache: true,
  //     //     link: "",
  //     //     activeMenu: "",
  //     //   },
  //     //   name: "demo",
  //     //   path: "/btn/demo",
  //     //   children: [
  //     {
  //       alwaysShow: false,
  //       component: "demo/btn/index",
  //       hidden: false,
  //       meta: {
  //         title: "按钮组件示例",
  //         titleEn: "Icon Btn",
  //         icon: "",
  //         noCache: true,
  //         link: "",
  //         activeMenu: "",
  //       },
  //       name: "btnDemo1",
  //       path: "/btn/demo/btn1",
  //       children: [],
  //       query: "",
  //       pid: "1698621081462706327",
  //       id: "1698621081462706324",
  //     },
  //     {
  //       alwaysShow: false,
  //       component: "demo/swform/index",
  //       hidden: false,
  //       meta: {
  //         title: "表单项管控",
  //         titleEn: "Dict Style",
  //         icon: "",
  //         noCache: true,
  //         link: "",
  //         activeMenu: "",
  //       },
  //       name: "swFormDemo",
  //       path: "/dict/demo/swform",
  //       children: [],
  //       query: "",
  //       pid: "1698621081462706326",
  //       id: "1698621081462706324",
  //     },
  //     {
  //       alwaysShow: false,
  //       component: "demo/formItem/index",
  //       hidden: false,
  //       meta: {
  //         title: "表单项组件示例",
  //         titleEn: "Form Item",
  //         icon: "",
  //         noCache: true,
  //         link: "",
  //         activeMenu: "",
  //       },
  //       name: "formItemDemo",
  //       path: "/btn/demo/formItem",
  //       children: [],
  //       query: "",
  //       pid: "1698621081462706327",
  //       id: "1698621081462706324",
  //     },
  //     {
  //       alwaysShow: false,
  //       component: "demo/form/index",
  //       hidden: false,
  //       meta: {
  //         title: "表单组件示例",
  //         titleEn: "Form",
  //         icon: "",
  //         noCache: true,
  //         link: "",
  //         activeMenu: "",
  //       },
  //       name: "formDemo",
  //       path: "/btn/demo/form",
  //       children: [],
  //       query: "",
  //       pid: "1698621081462706327",
  //       id: "1698621081462706324",
  //     },
  //     //   ],
  //     //   query: "",
  //     //   pid: "1698621081462707497",
  //     //   id: "1698621081462706327",
  //     // },
  //     // {
  //     //   alwaysShow: true,
  //     //   component: "ParentView",
  //     //   hidden: false,
  //     //   meta: {
  //     //     title: "分页列表",
  //     //     titleEn: "Btn",
  //     //     icon: "",
  //     //     noCache: true,
  //     //     link: "",
  //     //     activeMenu: "",
  //     //   },
  //     //   name: "demo",
  //     //   path: "/list/demo",
  //     //   children: [
  //     {
  //       alwaysShow: false,
  //       component: "demo/searchtable/index",
  //       hidden: false,
  //       meta: {
  //         title: "表格列表组件示例",
  //         titleEn: "List",
  //         icon: "",
  //         noCache: true,
  //         link: "",
  //         activeMenu: "",
  //       },
  //       name: "searchTableList",
  //       path: "/list/demo/searchTableList",
  //       children: [],
  //       query: "",
  //       pid: "1698621081462706327",
  //       id: "1698621081462706324",
  //     },
  //     {
  //       alwaysShow: false,
  //       component: "demo/searchlist/index",
  //       hidden: false,
  //       meta: {
  //         title: "卡片列表组件示例",
  //         titleEn: "List",
  //         icon: "",
  //         noCache: true,
  //         link: "",
  //         activeMenu: "",
  //       },
  //       name: "searchListList",
  //       path: "/list/demo/searchListList",
  //       children: [],
  //       query: "",
  //       pid: "1698621081462706327",
  //       id: "1698621081462706324",
  //     },
  //     {
  //       alwaysShow: false,
  //       component: "demo/panel/chart",
  //       hidden: false,
  //       meta: {
  //         title: "Echarts组件示例",
  //         titleEn: "Echarts",
  //         icon: "",
  //         noCache: true,
  //         link: "",
  //         activeMenu: "",
  //       },
  //       name: "Echarts",
  //       path: "/list/demo/chart",
  //       children: [],
  //       query: "",
  //       pid: "1698621081462706327",
  //       id: "1698621081462706324",
  //     },
  //     {
  //       alwaysShow: false,
  //       component: "demo/panel/statistics",
  //       hidden: false,
  //       meta: {
  //         title: "统计组件示例",
  //         titleEn: "Statistics",
  //         icon: "",
  //         noCache: true,
  //         link: "",
  //         activeMenu: "",
  //       },
  //       name: "Statistics",
  //       path: "/list/demo/statistics",
  //       children: [],
  //       query: "",
  //       pid: "1698621081462706327",
  //       id: "1698621081462706324",
  //     },
  //     //   ],
  //     //   query: "",
  //     //   pid: "1698621081462707497",
  //     //   id: "1698621081462706327",
  //     // },
  //     // {
  //     //   alwaysShow: true,
  //     //   component: "ParentView",
  //     //   hidden: false,
  //     //   meta: {
  //     //     title: "图标示例",
  //     //     titleEn: "demo",
  //     //     icon: "",
  //     //     noCache: true,
  //     //     link: "",
  //     //     activeMenu: "",
  //     //   },
  //     //   name: "demo",
  //     //   path: "/icon",
  //     //   children: [
  //     {
  //       alwaysShow: false,
  //       component: "develop/iconlist/index",
  //       hidden: false,
  //       meta: {
  //         title: "图标素材",
  //         titleEn: "demo List",
  //         icon: "",
  //         noCache: true,
  //         link: "",
  //         activeMenu: "",
  //       },
  //       name: "demoList001",
  //       path: "icons",
  //       children: [],
  //       query: "",
  //       pid: "1698621081462706324",
  //       id: "16986210814627063299",
  //     },
  //     {
  //       alwaysShow: false,
  //       component: "demo/textTip/index",
  //       hidden: false,
  //       meta: {
  //         title: "文字提示",
  //         titleEn: "Text Tip",
  //         icon: "",
  //         noCache: true,
  //         link: "",
  //         activeMenu: "",
  //       },
  //       name: "textTip",
  //       path: "textTip",
  //       children: [],
  //       query: "",
  //       pid: "1698621081462706324",
  //       id: "16986210814627063298",
  //     },
  //     //   ],
  //     //   query: "",
  //     //   pid: "1698621081462707497",
  //     //   id: "1698621081462706324",
  //     // },
  //   ],
  //   query: "",
  //   pid: "",
  //   id: "1698621081462707497",
  // },
  // {
  //   alwaysShow: true,
  //   component: "Layout",
  //   hidden: false,
  //   meta: {
  //     title: "参考页面示例",
  //     titleEn: "Page Demo",
  //     icon: "apiButt",
  //     noCache: true,
  //     link: "",
  //     activeMenu: "",
  //   },
  //   name: "page",
  //   path: "/page",
  //   children: [
  //     {
  //       alwaysShow: true,
  //       component: "ParentView",
  //       hidden: false,
  //       meta: {
  //         title: "列表示例",
  //         titleEn: "demo",
  //         icon: "",
  //         noCache: true,
  //         link: "",
  //         activeMenu: "",
  //       },
  //       name: "demo",
  //       path: "/list/demo",
  //       children: [
  //         {
  //           alwaysShow: false,
  //           component: "demo/searchtable/components/list1",
  //           hidden: false,
  //           meta: {
  //             title: "列表",
  //             titleEn: "demo List",
  //             icon: "",
  //             noCache: true,
  //             link: "",
  //             activeMenu: "",
  //           },
  //           name: "demoList001",
  //           path: "list1",
  //           children: [],
  //           query: "",
  //           pid: "1698621081462706323",
  //           id: "16986210814627063387",
  //         },
  //         {
  //           alwaysShow: false,
  //           component: "demo/list/detail",
  //           hidden: false,
  //           meta: {
  //             title: "二级列表",
  //             titleEn: "List Detail",
  //             icon: "",
  //             noCache: true,
  //             link: "",
  //             activeMenu: "",
  //           },
  //           name: "demoDetail",
  //           path: "list1/detail",
  //           children: [],
  //           query: "",
  //           pid: "1698621081462706323",
  //           id: "16986210814627063298",
  //         },
  //         {
  //           alwaysShow: false,
  //           component: "demo/list/detail1",
  //           hidden: true,
  //           meta: {
  //             title: "详情",
  //             titleEn: "Detail",
  //             icon: "",
  //             noCache: true,
  //             link: "",
  //             activeMenu: "/list/demo/list1",
  //           },
  //           name: "demoDetail1",
  //           path: "list1/detail1",
  //           children: [],
  //           query: "",
  //           pid: "1698621081462706323",
  //           id: "16986210814627063297",
  //         },
  //         {
  //           alwaysShow: false,
  //           component: "demo/searchtable/components/list5",
  //           hidden: false,
  //           meta: {
  //             title: "树级结构列表",
  //             titleEn: "demo List",
  //             icon: "",
  //             noCache: true,
  //             link: "",
  //             activeMenu: "",
  //           },
  //           name: "demoList005",
  //           path: "list5",
  //           children: [],
  //           query: "",
  //           pid: "1698621081462706323",
  //           id: "16986210814627063210",
  //         },
  //         {
  //           alwaysShow: false,
  //           component: "demo/searchtable/components/list6",
  //           hidden: false,
  //           meta: {
  //             title: "左侧+列表",
  //             titleEn: "demo List",
  //             icon: "",
  //             noCache: true,
  //             link: "",
  //             activeMenu: "",
  //           },
  //           name: "demoList003",
  //           path: "list6",
  //           children: [],
  //           query: "",
  //           pid: "1698621081462706323",
  //           id: "16986210814627063211",
  //         },
  //         {
  //           alwaysShow: false,
  //           component: "demo/searchtable/components/list7",
  //           hidden: false,
  //           meta: {
  //             title: "右侧+列表",
  //             titleEn: "demo List",
  //             icon: "",
  //             noCache: true,
  //             link: "",
  //             activeMenu: "",
  //           },
  //           name: "demoList002",
  //           path: "list7",
  //           children: [],
  //           query: "",
  //           pid: "1698621081462706323",
  //           id: "16986210814627063210",
  //         },
  //         {
  //           alwaysShow: false,
  //           component: "demo/list/index3",
  //           hidden: false,
  //           meta: {
  //             title: "手动查询列表",
  //             titleEn: "demo List",
  //             icon: "",
  //             noCache: true,
  //             link: "",
  //             activeMenu: "",
  //           },
  //           name: "demoList004",
  //           path: "list4",
  //           children: [],
  //           query: "",
  //           pid: "1698621081462706323",
  //           id: "16986210814627063212",
  //         },
  //         {
  //           alwaysShow: false,
  //           component: "demo/searchlist/components/list1",
  //           hidden: false,
  //           meta: {
  //             title: "卡片列表1",
  //             titleEn: "demo List",
  //             icon: "",
  //             noCache: true,
  //             link: "",
  //             activeMenu: "",
  //           },
  //           name: "demoList001",
  //           path: "cardlist1",
  //           children: [],
  //           query: "",
  //           pid: "1698621081462706323",
  //           id: "16986210814627063299",
  //         },
  //         {
  //           alwaysShow: false,
  //           component: "demo/searchlist/components/list2",
  //           hidden: false,
  //           meta: {
  //             title: "卡片列表2",
  //             titleEn: "demo List",
  //             icon: "",
  //             noCache: true,
  //             link: "",
  //             activeMenu: "",
  //           },
  //           name: "demoList002",
  //           path: "cardlist2",
  //           children: [],
  //           query: "",
  //           pid: "1698621081462706323",
  //           id: "16986210814627063299",
  //         },
  //         {
  //           alwaysShow: false,
  //           component: "demo/searchlist/components/list3",
  //           hidden: false,
  //           meta: {
  //             title: "大数据卡片列表",
  //             titleEn: "demo List",
  //             icon: "",
  //             noCache: true,
  //             link: "",
  //             activeMenu: "",
  //           },
  //           name: "demoList009",
  //           path: "cardlist3",
  //           children: [],
  //           query: "",
  //           pid: "1698621081462706323",
  //           id: "16986210814627063299",
  //         },
  //       ],
  //       query: "",
  //       pid: "1698621081462707498",
  //       id: "1698621081462706323",
  //     },
  //     {
  //       alwaysShow: true,
  //       component: "ParentView",
  //       hidden: false,
  //       meta: {
  //         title: "详情示例",
  //         titleEn: "demo",
  //         icon: "",
  //         noCache: true,
  //         link: "",
  //         activeMenu: "",
  //       },
  //       name: "demo",
  //       path: "/detail/demo",
  //       children: [
  //         {
  //           alwaysShow: false,
  //           component: "demo/list/detail1",
  //           hidden: false,
  //           meta: {
  //             title: "详情",
  //             titleEn: "Detail",
  //             icon: "",
  //             noCache: true,
  //             link: "",
  //             activeMenu: "",
  //           },
  //           name: "demoDetail1",
  //           path: "list1/detail1",
  //           children: [],
  //           query: "",
  //           pid: "16986210814627063233",
  //           id: "16986210814627063297",
  //         },
  //       ],
  //       query: "",
  //       pid: "1698621081462707498",
  //       id: "1698621081462706333",
  //     },
  //     {
  //       alwaysShow: false,
  //       component: "ParentView",
  //       hidden: false,
  //       meta: {
  //         title: "表单示例",
  //         titleEn: "demo",
  //         icon: "",
  //         noCache: true,
  //         link: "",
  //         activeMenu: "",
  //       },
  //       name: "demo",
  //       path: "/form/demo",
  //       children: [
  //         {
  //           alwaysShow: false,
  //           component: "demo/form/components/index1",
  //           hidden: false,
  //           meta: {
  //             title: "单个表单",
  //             titleEn: "Form",
  //             icon: "",
  //             noCache: true,
  //             link: "",
  //             activeMenu: "",
  //           },
  //           name: "formDemo1",
  //           path: "form1",
  //           children: [],
  //           query: "",
  //           pid: "1698621081462706325",
  //           id: "1698621081462706324",
  //         },
  //         {
  //           alwaysShow: false,
  //           component: "demo/form/components/index2",
  //           hidden: false,
  //           meta: {
  //             title: "单个表单项动态添加",
  //             titleEn: "Form",
  //             icon: "",
  //             noCache: true,
  //             link: "",
  //             activeMenu: "",
  //           },
  //           name: "formDemo2",
  //           path: "form2",
  //           children: [],
  //           query: "",
  //           pid: "1698621081462706325",
  //           id: "1698621081462706325",
  //         },
  //         {
  //           alwaysShow: false,
  //           component: "demo/form/components/index3",
  //           hidden: false,
  //           meta: {
  //             title: "多个表单项动态添加",
  //             titleEn: "Form",
  //             icon: "",
  //             noCache: true,
  //             link: "",
  //             activeMenu: "",
  //           },
  //           name: "formDemo3",
  //           path: "form3",
  //           children: [],
  //           query: "",
  //           pid: "1698621081462706325",
  //           id: "1698621081462706326",
  //         },
  //         {
  //           alwaysShow: false,
  //           component: "demo/form/components/index4",
  //           hidden: false,
  //           meta: {
  //             title: "动态表单",
  //             titleEn: "Dynamic Form",
  //             icon: "",
  //             noCache: true,
  //             link: "",
  //             activeMenu: "",
  //           },
  //           name: "formDemo4",
  //           path: "form4",
  //           children: [],
  //           query: "",
  //           pid: "1698621081462706325",
  //           id: "1698621081462706327",
  //         },
  //         {
  //           alwaysShow: false,
  //           component: "demo/form/components/index5/index",
  //           hidden: false,
  //           meta: {
  //             title: "个性化表单",
  //             titleEn: "Personalized Form",
  //             icon: "",
  //             noCache: true,
  //             link: "",
  //             activeMenu: "",
  //           },
  //           name: "formDemo5",
  //           path: "form5",
  //           children: [],
  //           query: "",
  //           pid: "1698621081462706325",
  //           id: "1698621081462706328",
  //         },
  //         {
  //           alwaysShow: false,
  //           component: "demo/form/components/index6",
  //           hidden: false,
  //           meta: {
  //             title: "条件判断表单",
  //             titleEn: "Condition Judge Form",
  //             icon: "",
  //             noCache: true,
  //             link: "",
  //             activeMenu: "",
  //           },
  //           name: "formDemo6",
  //           path: "form6",
  //           children: [],
  //           query: "",
  //           pid: "1698621081462706325",
  //           id: "1698621081462706329",
  //         },
  //         {
  //           alwaysShow: false,
  //           component: "demo/form/components/index7",
  //           hidden: false,
  //           meta: {
  //             title: "常用表单校验规则",
  //             titleEn: "Validate Form",
  //             icon: "",
  //             noCache: true,
  //             link: "",
  //             activeMenu: "",
  //           },
  //           name: "formDemo7",
  //           path: "form7",
  //           children: [],
  //           query: "",
  //           pid: "1698621081462706325",
  //           id: "1698621081462706330",
  //         },
  //         // {
  //         //   alwaysShow: false,
  //         //   component: "demo/process/index",
  //         //   hidden: false,
  //         //   meta: {
  //         //     title: "步骤条式表单",
  //         //     titleEn: "Step Form",
  //         //     icon: "",
  //         //     noCache: true,
  //         //     link: "",
  //         //     activeMenu: "",
  //         //   },
  //         //   name: "process1",
  //         //   path: "process1",
  //         //   children: [],
  //         //   query: "",
  //         //   pid: "1698621081462706325",
  //         //   id: "1698621081462706331",
  //         // },
  //       ],
  //       query: "",
  //       pid: "1698621081462707498",
  //       id: "1698621081462706325",
  //     },
  //     {
  //       alwaysShow: true,
  //       component: "ParentView",
  //       hidden: false,
  //       meta: {
  //         title: "弹窗示例",
  //         titleEn: "Dialog Demo",
  //         icon: "",
  //         noCache: true,
  //         link: "",
  //         activeMenu: "",
  //       },
  //       name: "demo",
  //       path: "/dialog/demo",
  //       children: [
  //         {
  //           alwaysShow: false,
  //           component: "demo/dialog/index",
  //           hidden: false,
  //           meta: {
  //             title: "弹窗示例",
  //             titleEn: "dialog",
  //             icon: "",
  //             noCache: true,
  //             link: "",
  //             activeMenu: "",
  //           },
  //           name: "dialog1",
  //           path: "dialog1",
  //           children: [],
  //           query: "",
  //           pid: "1698621081462706328",
  //           id: "1698621081462706381",
  //         },
  //       ],
  //       query: "",
  //       pid: "1698621081462707498",
  //       id: "1698621081462706328",
  //     },
  //     {
  //       alwaysShow: true,
  //       component: "ParentView",
  //       hidden: false,
  //       meta: {
  //         title: "首页/面板示例",
  //         titleEn: "Panel Demo",
  //         icon: "",
  //         noCache: true,
  //         link: "",
  //         activeMenu: "",
  //       },
  //       name: "demo",
  //       path: "/panel/demo",
  //       children: [
  //         {
  //           alwaysShow: false,
  //           component: "demo/panel/panel1",
  //           hidden: false,
  //           meta: {
  //             title: "布局排列",
  //             titleEn: "Layout Panel",
  //             icon: "",
  //             noCache: true,
  //             link: "",
  //             activeMenu: "",
  //           },
  //           name: "panel1",
  //           path: "panel1",
  //           children: [],
  //           query: "",
  //           pid: "1698621081462706329",
  //           id: "1698621081462706391",
  //         },
  //         {
  //           alwaysShow: false,
  //           component: "demo/panel/panel2",
  //           hidden: false,
  //           meta: {
  //             title: "内容展示",
  //             titleEn: "Content Panel",
  //             icon: "",
  //             noCache: true,
  //             link: "",
  //             activeMenu: "",
  //           },
  //           name: "panel2",
  //           path: "panel2",
  //           children: [],
  //           query: "",
  //           pid: "1698621081462706329",
  //           id: "1698621081462706391",
  //         },
  //       ],
  //       query: "",
  //       pid: "1698621081462707498",
  //       id: "1698621081462706329",
  //     },
  //   ],
  //   query: "",
  //   pid: "",
  //   id: "1698621081462707498",
  // },
  // {
  //   alwaysShow: true,
  //   component: "Layout",
  //   hidden: false,
  //   meta: {
  //     title: "主题布局示例",
  //     titleEn: "Page Demo",
  //     icon: "apiButt",
  //     noCache: true,
  //     link: "",
  //     activeMenu: "",
  //   },
  //   name: "demoLayout",
  //   path: "/demoLayout",
  //   children: [
  //     {
  //       alwaysShow: false,
  //       component: "system/themeConfig/index",
  //       hidden: false,
  //       meta: {
  //         title: "主题控制",
  //         titleEn: "demo",
  //         icon: "",
  //         noCache: true,
  //         link: "",
  //         activeMenu: "",
  //       },
  //       name: "demoThemeLayout",
  //       path: "/demoLayout/theme",
  //       query: "",
  //       pid: "1698621081462707498",
  //       id: "1698621081462706323",
  //     },
  //     {
  //       alwaysShow: false,
  //       component: "demo/layout/index",
  //       hidden: false,
  //       meta: {
  //         title: "布局示例",
  //         titleEn: "demo",
  //         icon: "",
  //         noCache: true,
  //         link: "",
  //         activeMenu: "",
  //       },
  //       name: "demoLayout",
  //       path: "/demoLayout/demo",
  //       query: "",
  //       pid: "1698621081462707498",
  //       id: "1698621081462706323",
  //     },
  //   ],
  //   query: "",
  //   pid: "",
  //   id: "1698621081462707498",
  // },
];
